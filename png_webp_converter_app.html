<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PNG & JPG to WebP Converter</title>
  <script src="js/jszip.min.js"></script>
  <link rel="stylesheet" href="style.css"/>
</head>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2rem;
            font-weight: 600;
        }

        .drop-zone {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 20px;
            margin: 30px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9ff;
        }

        .drop-zone:hover, .drop-zone.dragover {
            border-color: #764ba2;
            background: #f0f2ff;
            transform: translateY(-2px);
        }

        .drop-zone-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
        }

        .drop-text {
            color: #666;
            font-size: 1.1rem;
        }

        .file-input {
            display: none;
        }

        .settings {
            background: #f5f5f5;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .setting-group {
            margin-bottom: 15px;
        }

        .setting-group:last-child {
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }

        .quality-display {
            text-align: center;
            color: #667eea;
            font-weight: 600;
        }

        .convert-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .convert-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .convert-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress {
            margin-top: 20px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .file-list {
            text-align: left;
            margin-top: 20px;
        }

        .file-item {
            padding: 10px;
            background: #f8f9fa;
            margin: 5px 0;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .download-section {
            margin-top: 30px;
            display: none;
        }

        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
        }

        .download-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PNG to WebP Converter</h1>
        
        <div class="drop-zone" id="dropZone">
            <div class="drop-zone-content">
                <div class="upload-icon">📁</div>
                <div class="drop-text">
                    Drop PNG files here or click to browse
                </div>
            </div>
        </div>
        
        <input type="file" id="fileInput" class="file-input" multiple accept=".png">
        
        <div class="settings">
            <div class="setting-group">
                <label for="quality">WebP Quality:</label>
                <input type="range" id="quality" min="0" max="100" value="80">
                <div class="quality-display" id="qualityDisplay">80%</div>
            </div>
        </div>
        
        <button class="convert-btn" id="convertBtn" disabled>
            Select PNG files to convert
        </button>
        
        <div class="progress" id="progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">Converting...</div>
        </div>
        
        <div class="status" id="status"></div>
        
        <div class="file-list" id="fileList"></div>
        
        <div class="download-section" id="downloadSection">
            <h3>Download Converted Files:</h3>
            <div id="downloadButtons"></div>
        </div>
    </div>

    <script>
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const convertBtn = document.getElementById('convertBtn');
        const qualitySlider = document.getElementById('quality');
        const qualityDisplay = document.getElementById('qualityDisplay');
        const progress = document.getElementById('progress');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const status = document.getElementById('status');
        const fileList = document.getElementById('fileList');
        const downloadSection = document.getElementById('downloadSection');
        const downloadButtons = document.getElementById('downloadButtons');
        
        let selectedFiles = [];
        let convertedFiles = [];

        // Quality slider
        qualitySlider.addEventListener('input', (e) => {
            qualityDisplay.textContent = e.target.value + '%';
        });

        // Drag and drop
        dropZone.addEventListener('click', () => fileInput.click());
        
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });
        
        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
        
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            selectedFiles = Array.from(files).filter(file => 
                file.type === 'image/png' || file.name.toLowerCase().endsWith('.png')
            );
            
            if (selectedFiles.length === 0) {
                showStatus('Please select PNG files only.', 'error');
                return;
            }
            
            updateFileList();
            convertBtn.disabled = false;
            convertBtn.textContent = `Convert ${selectedFiles.length} PNG file${selectedFiles.length > 1 ? 's' : ''}`;
        }

        function updateFileList() {
            fileList.innerHTML = '';
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span>${file.name}</span>
                    <span>${(file.size / 1024 / 1024).toFixed(2)} MB</span>
                `;
                fileList.appendChild(fileItem);
            });
        }

        convertBtn.addEventListener('click', convertFiles);

        async function convertFiles() {
            if (selectedFiles.length === 0) return;
            
            convertBtn.disabled = true;
            progress.style.display = 'block';
            status.style.display = 'none';
            downloadSection.style.display = 'none';
            convertedFiles = [];
            
            const quality = parseInt(qualitySlider.value);
            
            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                progressText.textContent = `Converting ${file.name}... (${i + 1}/${selectedFiles.length})`;
                progressFill.style.width = `${((i + 1) / selectedFiles.length) * 100}%`;
                
                try {
                    const webpBlob = await convertToWebP(file, quality);
                    const fileName = file.name.replace(/\.png$/i, '.webp');
                    convertedFiles.push({ blob: webpBlob, name: fileName });
                } catch (error) {
                    console.error('Error converting', file.name, error);
                }
                
                // Small delay to show progress
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            progress.style.display = 'none';
            
            if (convertedFiles.length > 0) {
                showStatus(`Successfully converted ${convertedFiles.length} file${convertedFiles.length > 1 ? 's' : ''}!`, 'success');
                showDownloadButtons();
            } else {
                showStatus('Failed to convert files. Please try again.', 'error');
            }
            
            convertBtn.disabled = false;
            convertBtn.textContent = 'Convert Again';
        }

        function convertToWebP(file, quality) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                img.onload = () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    
                    canvas.toBlob((blob) => {
                        if (blob) {
                            resolve(blob);
                        } else {
                            reject(new Error('Failed to convert to WebP'));
                        }
                    }, 'image/webp', quality / 100);
                };
                
                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = URL.createObjectURL(file);
            });
        }

        function showStatus(message, type) {
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        function showDownloadButtons() {
            downloadSection.style.display = 'block';
            downloadButtons.innerHTML = '';
            
            // Individual download buttons
            convertedFiles.forEach((file, index) => {
                const btn = document.createElement('button');
                btn.className = 'download-btn';
                btn.textContent = `Download ${file.name}`;
                btn.onclick = () => downloadFile(file.blob, file.name);
                downloadButtons.appendChild(btn);
            });
            
            // Download all as ZIP (if multiple files)
            if (convertedFiles.length > 1) {
                const zipBtn = document.createElement('button');
                zipBtn.className = 'download-btn';
                zipBtn.textContent = 'Download All as ZIP';
                zipBtn.onclick = downloadAllAsZip;
                downloadButtons.appendChild(zipBtn);
            }
        }

        function downloadFile(blob, filename) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        async function downloadAllAsZip() {
            if (convertedFiles.length === 0) return;
            
            showStatus('Creating ZIP file...', 'success');
            
            try {
                const zip = new JSZip();
                
                // Add all converted files to the ZIP
                convertedFiles.forEach(file => {
                    zip.file(file.name, file.blob);
                });
                
                // Generate the ZIP file
                const zipBlob = await zip.generateAsync({type: "blob"});
                
                // Download the ZIP
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                downloadFile(zipBlob, `converted-webp-files-${timestamp}.zip`);
                
                showStatus('ZIP file downloaded successfully!', 'success');
            } catch (error) {
                console.error('Error creating ZIP:', error);
                showStatus('Failed to create ZIP file. Please try downloading files individually.', 'error');
            }
        }
    </script>
</body>
</html>